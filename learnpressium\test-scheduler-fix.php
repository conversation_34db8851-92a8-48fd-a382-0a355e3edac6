<?php
/**
 * Comprehensive Test for Enrollment Scheduler Fix
 * 
 * This script tests and fixes the critical issue where scheduled courses
 * never automatically activate when their scheduled time arrives.
 * 
 * USAGE: Access via browser: yoursite.com/wp-content/plugins/learnpressium/test-scheduler-fix.php
 */

// Load WordPress
if (!defined('ABSPATH')) {
    $wp_load_path = dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';
    if (file_exists($wp_load_path)) {
        require_once $wp_load_path;
    } else {
        die('WordPress not found. Please run this from within WordPress context.');
    }
}

// Security check
if (!current_user_can('manage_options')) {
    die('Access denied. Administrator privileges required.');
}

echo "<h1>🔧 LearnPressium Scheduler Fix & Test</h1>";
echo "<p><strong>Testing the critical fix for automatic course activation</strong></p>";

// Load required classes
require_once LEARNPRESSIUM_PLUGIN_DIR . 'includes/modules/enrollment/class-simple-enrollment-manager.php';
require_once LEARNPRESSIUM_PLUGIN_DIR . 'includes/modules/enrollment/class-enrollment-scheduler.php';

$simple_manager = new Simple_Enrollment_Manager();
$scheduler = new Enrollment_Scheduler();

echo "<h2>📊 Current System Status</h2>";

// Test 1: Check database tables
echo "<h3>1. Database Table Check</h3>";
global $wpdb;

$scheduled_table = $wpdb->prefix . 'learnpressium_enrollment_schedules';
$user_items_table = $wpdb->prefix . 'learnpress_user_items';

$scheduled_exists = $wpdb->get_var("SHOW TABLES LIKE '{$scheduled_table}'") == $scheduled_table;
$user_items_exists = $wpdb->get_var("SHOW TABLES LIKE '{$user_items_table}'") == $user_items_table;

echo $scheduled_exists ? "✅ Scheduled table exists<br>" : "❌ Scheduled table missing<br>";
echo $user_items_exists ? "✅ LearnPress user_items table exists<br>" : "❌ LearnPress user_items table missing<br>";

// Test 2: Check for pending schedules
echo "<h3>2. Pending Schedules Analysis</h3>";

$pending_schedules = $wpdb->get_results(
    "SELECT * FROM {$scheduled_table} WHERE status = 'pending' ORDER BY scheduled_start_date ASC"
);

echo "<p><strong>Total pending schedules: " . count($pending_schedules) . "</strong></p>";

if (count($pending_schedules) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>User</th><th>Course</th><th>Scheduled Start</th><th>Status</th><th>Due?</th></tr>";
    
    $current_time = current_time('mysql');
    $due_count = 0;
    
    foreach ($pending_schedules as $schedule) {
        $is_due = strtotime($schedule->scheduled_start_date) <= strtotime($current_time);
        if ($is_due) $due_count++;
        
        $user = get_user_by('id', $schedule->user_id);
        $course = get_post($schedule->course_id);
        
        echo "<tr>";
        echo "<td>{$schedule->schedule_id}</td>";
        echo "<td>" . ($user ? $user->display_name : "User #{$schedule->user_id}") . "</td>";
        echo "<td>" . ($course ? $course->post_title : "Course #{$schedule->course_id}") . "</td>";
        echo "<td>{$schedule->scheduled_start_date}</td>";
        echo "<td>{$schedule->status}</td>";
        echo "<td>" . ($is_due ? "🔥 DUE NOW!" : "⏰ Future") . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p><strong>🚨 {$due_count} schedules are DUE for activation!</strong></p>";
} else {
    echo "<p>No pending schedules found.</p>";
}

// Test 3: Check cron job status
echo "<h3>3. Cron Job Status</h3>";

$cron_hook = 'learnpressium_activate_scheduled_courses';
$next_scheduled = wp_next_scheduled($cron_hook);

if ($next_scheduled) {
    echo "✅ Cron job is scheduled for: " . date('Y-m-d H:i:s', $next_scheduled) . "<br>";
    echo "⏰ Next run in: " . human_time_diff(time(), $next_scheduled) . "<br>";
} else {
    echo "❌ Cron job is NOT scheduled!<br>";
    echo "<p><strong>🔧 Attempting to schedule cron job...</strong></p>";
    
    // Try to schedule it
    $scheduler->schedule_cron();
    
    $next_scheduled = wp_next_scheduled($cron_hook);
    if ($next_scheduled) {
        echo "✅ Cron job successfully scheduled for: " . date('Y-m-d H:i:s', $next_scheduled) . "<br>";
    } else {
        echo "❌ Failed to schedule cron job<br>";
    }
}

// Test 4: Manual activation test
echo "<h3>4. Manual Activation Test</h3>";

if (isset($_GET['run_activation']) && $_GET['run_activation'] == '1') {
    echo "<p><strong>🚀 Running manual activation...</strong></p>";
    
    $activation_count = $simple_manager->activate_due_schedules();
    
    if ($activation_count > 0) {
        echo "<p>🎉 <strong>SUCCESS! Activated {$activation_count} scheduled courses!</strong></p>";
        echo "<p>✅ These courses should now appear in the 'In Progress' tab for users.</p>";
        
        // Verify activation
        $activated_courses = $wpdb->get_results(
            "SELECT ui.*, u.display_name, p.post_title 
             FROM {$user_items_table} ui
             LEFT JOIN {$wpdb->users} u ON ui.user_id = u.ID
             LEFT JOIN {$wpdb->posts} p ON ui.item_id = p.ID
             WHERE ui.ref_type = 'learnpressium_auto' 
             AND ui.status = 'enrolled'
             ORDER BY ui.start_time DESC
             LIMIT 10"
        );
        
        if (count($activated_courses) > 0) {
            echo "<h4>Recently Activated Courses:</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>User</th><th>Course</th><th>Status</th><th>Start Time</th></tr>";
            
            foreach ($activated_courses as $course) {
                echo "<tr>";
                echo "<td>{$course->display_name}</td>";
                echo "<td>{$course->post_title}</td>";
                echo "<td>{$course->status}</td>";
                echo "<td>{$course->start_time}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p>ℹ️ No courses were due for activation at this time.</p>";
    }
} else {
    if ($due_count > 0) {
        echo "<p><a href='?run_activation=1' style='background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>🚀 RUN MANUAL ACTIVATION NOW</a></p>";
    } else {
        echo "<p>No courses are currently due for activation.</p>";
    }
}

// Test 5: Create test schedule for immediate testing
echo "<h3>5. Create Test Schedule</h3>";

if (isset($_GET['create_test']) && $_GET['create_test'] == '1') {
    $test_user_id = get_current_user_id();
    $courses = get_posts(array('post_type' => 'lp_course', 'numberposts' => 1));
    
    if (count($courses) > 0) {
        $test_course_id = $courses[0]->ID;
        $test_start_date = date('Y-m-d H:i:s', strtotime('-1 minute')); // 1 minute ago
        
        $result = $simple_manager->schedule_enrollment(
            $test_user_id, 
            $test_course_id, 
            $test_start_date, 
            null, 
            'Test schedule for immediate activation'
        );
        
        if ($result) {
            echo "<p>✅ Test schedule created! Course should activate immediately.</p>";
            echo "<p><a href='?run_activation=1'>🚀 Run activation to test</a></p>";
        } else {
            echo "<p>❌ Failed to create test schedule</p>";
        }
    } else {
        echo "<p>❌ No courses found to create test schedule</p>";
    }
} else {
    echo "<p><a href='?create_test=1' style='background: #46b450; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>🧪 CREATE TEST SCHEDULE</a></p>";
}

echo "<h2>📋 Summary & Recommendations</h2>";

if ($due_count > 0) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>";
    echo "<h4>⚠️ Action Required</h4>";
    echo "<p>You have {$due_count} scheduled courses that should have been activated but weren't due to the bug.</p>";
    echo "<p><strong>Recommendation:</strong> Run the manual activation above to immediately activate these courses.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h4>✅ System Status: Good</h4>";
    echo "<p>No overdue scheduled courses found. The automatic activation should work properly going forward.</p>";
    echo "</div>";
}

echo "<h3>🔧 Technical Fix Applied</h3>";
echo "<ul>";
echo "<li>✅ Fixed scheduler to look in correct database table (learnpressium_enrollment_schedules)</li>";
echo "<li>✅ Fixed statistics queries to use proper table structure</li>";
echo "<li>✅ Ensured cron job is properly scheduled every 15 minutes</li>";
echo "<li>✅ Added comprehensive logging for debugging</li>";
echo "</ul>";

echo "<p><em>Test completed at: " . current_time('mysql') . "</em></p>";
?>
