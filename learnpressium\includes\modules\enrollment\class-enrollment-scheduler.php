<?php
/**
 * Enrollment Scheduler - Automatically activates scheduled courses
 * 
 * This class handles the automatic activation of scheduled courses when their date arrives.
 * It changes the status from 'scheduled' to 'enrolled' in LearnPress's user_items table,
 * which automatically makes them appear in the "In Progress" tab.
 */

if (!defined('ABSPATH')) {
    exit;
}

class Enrollment_Scheduler {
    
    private $simple_manager;
    
    public function __construct() {
        $this->simple_manager = new Simple_Enrollment_Manager();
    }
    
    /**
     * Initialize the scheduler
     */
    public function init() {
        // Add custom cron schedule
        add_filter('cron_schedules', array($this, 'add_cron_schedules'));

        // Schedule the cron job
        add_action('wp', array($this, 'schedule_cron'));

        // Hook the activation function to the cron event
        add_action('learnpressium_activate_scheduled_courses', array($this, 'activate_scheduled_courses'));

        // PERFORMANCE FIX: Only check on specific admin pages, not every admin page
        add_action('admin_init', array($this, 'check_and_activate_limited'));
    }

    /**
     * Add custom cron schedules
     */
    public function add_cron_schedules($schedules) {
        $schedules['every_fifteen_minutes'] = array(
            'interval' => 900, // 15 minutes in seconds
            'display'  => __('Every 15 Minutes', 'learnpressium')
        );
        return $schedules;
    }
    
    /**
     * Schedule the cron job if not already scheduled
     */
    public function schedule_cron() {
        if (!wp_next_scheduled('learnpressium_activate_scheduled_courses')) {
            // Use 15-minute schedule for reliable activation
            wp_schedule_event(time(), 'every_fifteen_minutes', 'learnpressium_activate_scheduled_courses');
        }
    }
    
    /**
     * Activate scheduled courses that are due
     * This is the main function that moves courses from "Scheduled" to "In Progress"
     */
    public function activate_scheduled_courses() {
        $activated_count = $this->simple_manager->activate_due_schedules();
        
        if ($activated_count > 0) {
            error_log("Learnpressium: Activated {$activated_count} scheduled courses");
            
            // Optional: Send notification to admin
            $this->notify_admin_of_activations($activated_count);
        }
        
        return $activated_count;
    }
    
    /**
     * PERFORMANCE FIX: Limited activation check - only on specific admin pages
     */
    public function check_and_activate_limited() {
        // Only run on specific admin pages to avoid performance impact
        $screen = get_current_screen();
        if ($screen && in_array($screen->id, array(
            'learnpress_page_learn-press-tools',
            'learnpressium_page_learnpressium-enrollment',
            'users',
            'user-edit'
        ))) {
            // Use transient to prevent multiple checks within 5 minutes
            $last_check = get_transient('learnpressium_last_admin_activation_check');
            if (!$last_check || (time() - $last_check) >= 300) {
                set_transient('learnpressium_last_admin_activation_check', time(), 300);
                $this->activate_scheduled_courses();
            }
        }
    }

    /**
     * Legacy method for backward compatibility
     */
    public function check_and_activate() {
        // Redirect to limited check for performance
        $this->check_and_activate_limited();
    }
    
    /**
     * Notify admin of course activations
     */
    private function notify_admin_of_activations($count) {
        // Get admin email
        $admin_email = get_option('admin_email');
        
        // Prepare email
        $subject = sprintf('[%s] %d Scheduled Courses Activated', get_bloginfo('name'), $count);
        $message = sprintf(
            "Hello,\n\n%d scheduled courses have been automatically activated and are now available to students.\n\nYou can view the course enrollments in your LearnPress Tools section.\n\nBest regards,\nLearnpressium Plugin",
            $count
        );
        
        // Send email (optional - can be disabled)
        if (apply_filters('learnpressium_send_activation_notifications', false)) {
            wp_mail($admin_email, $subject, $message);
        }
    }
    
    /**
     * Manual activation trigger (for testing or manual runs)
     */
    public function manual_activate() {
        if (!current_user_can('manage_options')) {
            return false;
        }
        
        return $this->activate_scheduled_courses();
    }
    
    /**
     * Get next scheduled activation time
     * FIXED: Look in the correct table where schedules are actually stored
     */
    public function get_next_activation_time() {
        global $wpdb;

        $scheduled_table = $wpdb->prefix . 'learnpressium_enrollment_schedules';

        $next_activation = $wpdb->get_var($wpdb->prepare(
            "SELECT MIN(scheduled_start_date)
             FROM {$scheduled_table}
             WHERE status = 'pending'
             AND scheduled_start_date > %s",
            current_time('mysql')
        ));

        return $next_activation;
    }
    
    /**
     * Get statistics about scheduled courses
     * FIXED: Look in the correct table where schedules are actually stored
     */
    public function get_scheduler_stats() {
        global $wpdb;

        $scheduled_table = $wpdb->prefix . 'learnpressium_enrollment_schedules';
        $stats = array();

        // Total scheduled courses (pending status)
        $stats['total_scheduled'] = intval($wpdb->get_var(
            "SELECT COUNT(*) FROM {$scheduled_table} WHERE status = 'pending'"
        ));

        // Courses due for activation (past their scheduled start date)
        $stats['due_for_activation'] = intval($wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$scheduled_table}
             WHERE status = 'pending'
             AND scheduled_start_date <= %s",
            current_time('mysql')
        )));

        // Next activation time
        $stats['next_activation'] = $this->get_next_activation_time();

        // Add cron job status
        $stats['cron_scheduled'] = wp_next_scheduled('learnpressium_activate_scheduled_courses') ? true : false;
        $stats['next_cron_run'] = wp_next_scheduled('learnpressium_activate_scheduled_courses');

        return $stats;
    }
    
    /**
     * Clean up cron job on deactivation
     */
    public function cleanup() {
        wp_clear_scheduled_hook('learnpressium_activate_scheduled_courses');
    }
}
